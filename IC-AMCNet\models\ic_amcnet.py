"""
IC-AMCNet (Improved Convolutional Automatic Modulation Classification Network) PyTorch实现

原始论文: IC-AMCNet模型用于自动调制识别
原始实现: TensorFlow/Keras
本实现: PyTorch

模型结构:
1. 四个2D卷积层，包含Conv2D + MaxPool2D + Dropout
2. 全连接层进行分类
3. 高斯噪声正则化

主要特点:
- 使用2D卷积处理I/Q信号
- 多层卷积特征提取
- 高dropout正则化
- 高斯噪声增强鲁棒性
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math


class IC_AMCNet(nn.Module):
    """
    IC-AMCNet模型的PyTorch实现
    
    Args:
        in_channels (int): 输入通道数，默认为2 (I/Q)
        num_classes (int): 分类类别数
        sequence_length (int): 输入序列长度
        dropout_rate (float): Dropout比率，默认为0.4
        conv1_channels (int): 第一层卷积通道数，默认为64
        conv2_channels (int): 第二层卷积通道数，默认为64
        conv3_channels (int): 第三层卷积通道数，默认为128
        conv4_channels (int): 第四层卷积通道数，默认为128
        fc_units (int): 全连接层单元数，默认为128
        gaussian_noise_std (float): 高斯噪声标准差，默认为1.0
    """
    
    def __init__(self, 
                 in_channels=2,
                 num_classes=11,
                 sequence_length=128,
                 dropout_rate=0.4,
                 conv1_channels=64,
                 conv2_channels=64,
                 conv3_channels=128,
                 conv4_channels=128,
                 fc_units=128,
                 gaussian_noise_std=1.0):
        super(IC_AMCNet, self).__init__()
        
        self.in_channels = in_channels
        self.num_classes = num_classes
        self.sequence_length = sequence_length
        self.dropout_rate = dropout_rate
        self.conv1_channels = conv1_channels
        self.conv2_channels = conv2_channels
        self.conv3_channels = conv3_channels
        self.conv4_channels = conv4_channels
        self.fc_units = fc_units
        self.gaussian_noise_std = gaussian_noise_std
        
        # 第一个卷积层
        self.conv1 = nn.Conv2d(
            in_channels=1,  # 输入形状 [B, 1, 2, L]
            out_channels=conv1_channels,
            kernel_size=(1, 8),
            padding=(0, 4)  # same padding
        )
        
        # 第一个池化层 - 只在序列长度维度池化，保持I/Q通道
        self.maxpool1 = nn.MaxPool2d(kernel_size=(1, 2))
        
        # 第二个卷积层
        self.conv2 = nn.Conv2d(
            in_channels=conv1_channels,
            out_channels=conv2_channels,
            kernel_size=(1, 4),
            padding=(0, 2)  # same padding
        )
        
        # 第三个卷积层
        self.conv3 = nn.Conv2d(
            in_channels=conv2_channels,
            out_channels=conv3_channels,
            kernel_size=(1, 8),
            padding=(0, 4)  # same padding
        )
        
        # 第二个池化层（原始代码中是1x1，实际上不改变尺寸）
        self.maxpool2 = nn.MaxPool2d(kernel_size=(1, 1))
        
        # 第四个卷积层
        self.conv4 = nn.Conv2d(
            in_channels=conv3_channels,
            out_channels=conv4_channels,
            kernel_size=(1, 8),
            padding=(0, 4)  # same padding
        )
        
        # Dropout层
        self.dropout = nn.Dropout(dropout_rate)
        self.dropout2d = nn.Dropout2d(dropout_rate)
        
        # 计算展平后的特征维度
        self._calculate_flatten_dim()
        
        # 全连接层
        self.fc1 = nn.Linear(self.flatten_dim, fc_units)
        self.fc2 = nn.Linear(fc_units, num_classes)
        
        # 高斯噪声层（在训练时使用）
        self.gaussian_noise_std = gaussian_noise_std
        
        # 初始化权重
        self._initialize_weights()
    
    def _calculate_flatten_dim(self):
        """计算展平后的特征维度"""
        # 创建一个dummy输入来计算维度
        dummy_input = torch.zeros(1, 1, self.in_channels, self.sequence_length)
        
        # 第一个卷积和池化
        x = F.relu(self.conv1(dummy_input))
        x = self.maxpool1(x)
        
        # 第二个卷积
        x = F.relu(self.conv2(x))
        
        # 第三个卷积和池化
        x = F.relu(self.conv3(x))
        x = self.maxpool2(x)
        
        # 第四个卷积
        x = F.relu(self.conv4(x))
        
        # 计算展平维度
        self.flatten_dim = x.view(x.size(0), -1).size(1)
        
    def _initialize_weights(self):
        """初始化模型权重"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                # 使用Glorot uniform初始化（对应原始的glorot_uniform）
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                # 全连接层使用He normal初始化
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        """
        前向传播
        
        Args:
            x: 输入张量，形状为 [batch_size, 2, sequence_length]
            
        Returns:
            output: 分类输出，形状为 [batch_size, num_classes]
        """
        batch_size = x.size(0)
        
        # 调整输入形状: [B, 2, L] -> [B, 1, 2, L]
        if x.dim() == 3:
            x = x.unsqueeze(1)
        
        # 第一个卷积层和池化
        x = F.relu(self.conv1(x))
        x = self.maxpool1(x)
        
        # 第二个卷积层
        x = F.relu(self.conv2(x))
        
        # 第三个卷积层和池化
        x = F.relu(self.conv3(x))
        x = self.maxpool2(x)
        x = self.dropout2d(x)
        
        # 第四个卷积层
        x = F.relu(self.conv4(x))
        x = self.dropout2d(x)
        
        # 展平
        x = x.view(batch_size, -1)
        
        # 第一个全连接层
        x = F.relu(self.fc1(x))
        x = self.dropout(x)
        
        # 高斯噪声（仅在训练时）
        if self.training and self.gaussian_noise_std > 0:
            noise = torch.randn_like(x) * self.gaussian_noise_std
            x = x + noise
        
        # 输出层
        x = self.fc2(x)
        
        return x
    
    def get_model_info(self):
        """获取模型信息"""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        return {
            'model_name': 'IC-AMCNet',
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'input_shape': f'[batch_size, {self.in_channels}, {self.sequence_length}]',
            'output_shape': f'[batch_size, {self.num_classes}]',
            'conv1_channels': self.conv1_channels,
            'conv2_channels': self.conv2_channels,
            'conv3_channels': self.conv3_channels,
            'conv4_channels': self.conv4_channels,
            'fc_units': self.fc_units,
            'dropout_rate': self.dropout_rate,
            'gaussian_noise_std': self.gaussian_noise_std,
            'flatten_dim': self.flatten_dim
        }


def create_ic_amcnet_for_dataset(dataset_type, num_classes, sequence_length):
    """
    为特定数据集创建IC-AMCNet模型
    
    Args:
        dataset_type (str): 数据集类型
        num_classes (int): 类别数
        sequence_length (int): 序列长度
        
    Returns:
        IC_AMCNet: 配置好的IC-AMCNet模型
    """
    # 根据数据集调整参数
    if dataset_type == 'rml':
        # RML数据集参数
        config = {
            'conv1_channels': 64,
            'conv2_channels': 64,
            'conv3_channels': 128,
            'conv4_channels': 128,
            'fc_units': 128,
            'dropout_rate': 0.4,
            'gaussian_noise_std': 1.0
        }
    elif dataset_type == 'hisar':
        # HisarMod数据集参数
        config = {
            'conv1_channels': 80,
            'conv2_channels': 80,
            'conv3_channels': 160,
            'conv4_channels': 160,
            'fc_units': 256,
            'dropout_rate': 0.3,
            'gaussian_noise_std': 0.8
        }
    elif dataset_type.startswith('torchsig'):
        # TorchSig数据集参数
        config = {
            'conv1_channels': 96,
            'conv2_channels': 96,
            'conv3_channels': 192,
            'conv4_channels': 192,
            'fc_units': 512,
            'dropout_rate': 0.25,
            'gaussian_noise_std': 0.5
        }
    else:
        # 默认参数
        config = {
            'conv1_channels': 64,
            'conv2_channels': 64,
            'conv3_channels': 128,
            'conv4_channels': 128,
            'fc_units': 128,
            'dropout_rate': 0.4,
            'gaussian_noise_std': 1.0
        }
    
    return IC_AMCNet(
        in_channels=2,
        num_classes=num_classes,
        sequence_length=sequence_length,
        **config
    )


if __name__ == '__main__':
    # 测试模型
    model = IC_AMCNet(num_classes=11, sequence_length=128)
    
    # 打印模型信息
    info = model.get_model_info()
    print("IC-AMCNet模型信息:")
    for key, value in info.items():
        print(f"  {key}: {value}")
    
    # 测试前向传播
    batch_size = 4
    test_input = torch.randn(batch_size, 2, 128)
    
    with torch.no_grad():
        output = model(test_input)
        print(f"\n测试输入形状: {test_input.shape}")
        print(f"测试输出形状: {output.shape}")
        print(f"输出范围: [{output.min().item():.4f}, {output.max().item():.4f}]")
